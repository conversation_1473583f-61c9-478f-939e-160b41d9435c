#!/usr/bin/env python3
from glob import glob
import os
from pathlib import Path
from platform import platform
import shutil
import sys
import subprocess
import filecmp
from datetime import datetime
from typing import Set, Any, List, Tuple
import traceback
from tempfile import TemporaryDirectory
import platform

# Parse arguments first
if len(sys.argv) < 2:
    print("Usage: secrets.py <command> <environment> [--dry-run]")
    print("Commands: encrypt, decrypt, diff-with-encrypted, diff-with-last-decrypted, cleanup")
    print("Environments: dev, test, prod")
    exit(1)

COMMAND = sys.argv[1]
DRY_RUN = "--dry-run" in sys.argv

if COMMAND not in ('encrypt', 'decrypt', 'diff-with-encrypted', 'diff-with-last-decrypted', 'cleanup'):
    print(f"Invalid command: {COMMAND}")
    print("Valid commands: encrypt, decrypt, diff-with-encrypted, diff-with-last-decrypted, cleanup")
    exit(1)

# For cleanup, environment is optional
if COMMAND == 'cleanup':
    ENVIRONMENT = sys.argv[2] if len(sys.argv) > 2 and sys.argv[2] != '--dry-run' else None
else:
    if len(sys.argv) < 3:
        print("Usage: secrets.py <command> <environment> [--dry-run]")
        print("Environment is required for this command")
        exit(1)
    ENVIRONMENT = sys.argv[2]

if COMMAND != 'cleanup' and ENVIRONMENT not in ('dev', 'test', 'prod'):
    print(f"Invalid environment: {ENVIRONMENT}")
    print("Valid environments: dev, test, prod")
    exit(1)

# Configuration
BACKUP_BASE_DIR = "backup"
platform_machine = platform.machine()
if platform_machine == 'x86_64':
    AGE_BINARY = "./age/amd64/age"
elif platform_machine == 'arm64':
    AGE_BINARY = './age/arm64/age'
else:
    raise AssertionError(f'unrecognized platform.machine() == {platform_machine}')

# New single directory structure
BASE_DIR = "environments"
ENVIRONMENT_DIR = f"{BASE_DIR}/{ENVIRONMENT}"

AGE_DECRYPTION_KEY = os.getenv("AGE_DECRYPTION_KEY", f"{Path.home()}/.ssh/id_rsa")
if not os.path.exists(AGE_DECRYPTION_KEY):
    print(f"ERROR: Decryption key not found at: {AGE_DECRYPTION_KEY}")
    print("To fix this:")
    print("- Make sure your RSA private key exists at the specified path")
    print("- Or set the AGE_DECRYPTION_KEY environment variable to the correct path")
    exit(1)

try:
    with open(AGE_DECRYPTION_KEY, 'r') as key_file:
        key_content = key_file.read()
        if not key_content.startswith('-----BEGIN') or 'PRIVATE KEY' not in key_content:
            print(f"ERROR: The file at '{AGE_DECRYPTION_KEY}' does not appear to be a valid private key")
            print("Expected format: PEM-encoded RSA private key starting with '-----BEGIN'")
            exit(1)
except PermissionError:
    print(f"ERROR: Permission denied reading decryption key at: {AGE_DECRYPTION_KEY}")
    print("Please check file permissions (should be readable by the current user)")
    exit(1)
except Exception as e:
    print(f"ERROR: Cannot read decryption key at '{AGE_DECRYPTION_KEY}': {e}")
    exit(1)

now = datetime.now()
BACKUP_DIR = f'{BACKUP_BASE_DIR}/{now.strftime("%Y_%m_%d-%H_%M_%S")}/{ENVIRONMENT}'

SECRET_PATTERNS = [
    "./**/*secret*/**/*",
    "./**/*secret*",
    "./**/*-postgres-cluster/certs/**/*",
    "./**/ssh_keys/**/*"
]

# Helper functions for new naming convention
def get_encrypted_name(original_path: str) -> str:
    """Convert a filename to its encrypted version."""
    # Remove existing .decrypted marker if present
    if '.decrypted' in original_path:
        original_path = original_path.replace('.decrypted', '')
    
    base, ext = os.path.splitext(original_path)
    if ext:  # Has extension
        return f"{base}.encrypted{ext}"
    else:  # No extension
        return f"{original_path}.encrypted"

def get_decrypted_name(original_path: str) -> str:
    """Convert a filename to its decrypted version."""
    # Remove existing .encrypted marker if present
    if '.encrypted' in original_path:
        original_path = original_path.replace('.encrypted', '')
    
    base, ext = os.path.splitext(original_path)
    if ext:  # Has extension
        return f"{base}.decrypted{ext}"
    else:  # No extension
        return f"{original_path}.decrypted"

def get_original_name(marked_path: str) -> str:
    """Get the original filename without encrypted/decrypted markers."""
    result = marked_path.replace('.encrypted', '').replace('.decrypted', '')
    return result

def is_already_marked(filepath: str) -> bool:
    """Check if a file already has encryption markers."""
    return '.encrypted' in filepath or '.decrypted' in filepath

def matches_secret_pattern(filepath: str) -> bool:
    """Check if a file matches any secret pattern."""
    # Simple pattern matching without expensive glob operations
    filename = os.path.basename(filepath)
    path_lower = filepath.lower()
    
    # Check if path contains secret-related keywords
    secret_keywords = ['secret', 'ssh_keys', 'certs', 'postgres-cluster']
    
    for keyword in secret_keywords:
        if keyword in path_lower:
            return True
    
    return False

def is_ssh_private_key(filepath: str) -> bool:
    """Check if a file is an SSH private key that needs 0600 permissions."""
    filepath_lower = filepath.lower()
    
    # Check if file is in ssh_keys directory and is not a public key
    if 'ssh_keys' in filepath_lower and not filepath.endswith('.pub'):
        return True
    
    return False

def find_secret_files(base_dir: str, state: str = 'encrypted') -> List[str]:
    """Find all secret files in given state (encrypted/decrypted/any)."""
    secret_files = []
    
    for pattern in SECRET_PATTERNS:
        matches = glob(f"{base_dir}/{pattern}", recursive=True)
        for match in matches:
            if not os.path.isfile(match):
                continue
                
            if state == 'encrypted' and '.encrypted' in match:
                secret_files.append(match)
            elif state == 'decrypted' and '.decrypted' in match:
                secret_files.append(match)
            elif state == 'any':
                secret_files.append(match)
            elif state == 'unmarked' and not is_already_marked(match):
                # Files that match patterns but don't have markers
                secret_files.append(match)
    
    return list(set(secret_files))  # Remove duplicates

def encrypt_file(src: str, dest: str, recipients_file: str) -> None:
    """Encrypt a single file using age."""
    subprocess.check_call(
        args=[AGE_BINARY, "-R", recipients_file, "-o", dest, src],
        stderr=subprocess.PIPE
    )

def decrypt_file(src: str, dest: str) -> None:
    """Decrypt a single file using age."""
    try:
        subprocess.check_call(
            args=[AGE_BINARY, "-d", "-i", AGE_DECRYPTION_KEY, "-o", dest, src],
            stderr=subprocess.PIPE
        )
        
        # Set proper permissions for SSH private keys
        if is_ssh_private_key(dest):
            os.chmod(dest, 0o600)
            
    except subprocess.CalledProcessError as e:
        stderr_output = e.stderr.decode('utf-8') if e.stderr else ""
        
        if "no identity matched any of the recipients" in stderr_output:
            print(f"ERROR: Decryption failed for '{src}'")
            print(f"The RSA private key at '{AGE_DECRYPTION_KEY}' does not match any of the recipients.")
            print("This usually means:")
            print("- You're using the wrong private key")
            print("- The file was encrypted for different recipients")
            print("- Your private key is not in the age_recipients.txt file")
            raise
        elif "failed to read identity" in stderr_output:
            print(f"ERROR: Cannot read the private key at '{AGE_DECRYPTION_KEY}'")
            print("Please check:")
            print("- The file exists and is readable")
            print("- The file contains a valid RSA private key")
            print("- You have the correct file permissions")
            raise
        else:
            print(f"ERROR: Age decryption failed for '{src}'")
            print(f"Exit code: {e.returncode}")
            if stderr_output:
                print(f"Error output: {stderr_output}")
            raise

def create_backup() -> None:
    """Create a backup of the current environment directory."""
    if os.path.exists(ENVIRONMENT_DIR):
        print(f"Creating backup at {BACKUP_DIR}")
        os.makedirs(os.path.dirname(BACKUP_DIR), exist_ok=True)
        shutil.copytree(ENVIRONMENT_DIR, BACKUP_DIR)

def encrypt_in_place() -> None:
    """Encrypt all decrypted secret files in place."""
    recipients_file = f"{ENVIRONMENT_DIR}/age_recipients.txt"
    if not os.path.exists(recipients_file):
        print(f"ERROR: Recipients file not found: {recipients_file}")
        exit(1)
    
    # Find all decrypted secret files
    decrypted_files = find_secret_files(ENVIRONMENT_DIR, 'decrypted')
    
    if not decrypted_files:
        print("No decrypted files found to encrypt")
        return
    
    print(f"Found {len(decrypted_files)} decrypted files to encrypt")
    
    for dec_file in decrypted_files:
        enc_file = dec_file.replace('.decrypted', '.encrypted')
        
        if DRY_RUN:
            print(f"DRY RUN: Would encrypt {dec_file} -> {enc_file}")
        else:
            print(f"Encrypting: {dec_file} -> {enc_file}")
            encrypt_file(dec_file, enc_file, recipients_file)
            os.remove(dec_file)
            print(f"Removed: {dec_file}")

def decrypt_in_place() -> None:
    """Decrypt all encrypted secret files in place."""
    # Find all encrypted secret files
    encrypted_files = find_secret_files(ENVIRONMENT_DIR, 'encrypted')
    
    if not encrypted_files:
        print("No encrypted files found to decrypt")
        return
    
    print(f"Found {len(encrypted_files)} encrypted files to decrypt")
    
    for enc_file in encrypted_files:
        dec_file = enc_file.replace('.encrypted', '.decrypted')
        
        if DRY_RUN:
            print(f"DRY RUN: Would decrypt {enc_file} -> {dec_file}")
        else:
            print(f"Decrypting: {enc_file} -> {dec_file}")
            decrypt_file(enc_file, dec_file)
            os.remove(enc_file)
            print(f"Removed: {enc_file}")

def show_status() -> None:
    """Show current status of encrypted/decrypted files."""
    encrypted_files = find_secret_files(ENVIRONMENT_DIR, 'encrypted')
    decrypted_files = find_secret_files(ENVIRONMENT_DIR, 'decrypted')
    unmarked_secrets = find_secret_files(ENVIRONMENT_DIR, 'unmarked')
    
    print(f"\nEnvironment: {ENVIRONMENT}")
    print(f"Directory: {ENVIRONMENT_DIR}")
    print(f"\nStatus:")
    print(f"- Encrypted files: {len(encrypted_files)}")
    print(f"- Decrypted files: {len(decrypted_files)}")
    print(f"- Unmarked secrets: {len(unmarked_secrets)}")
    
    if unmarked_secrets:
        print(f"\nWarning: Found {len(unmarked_secrets)} secret files without .encrypted/.decrypted markers:")
        for file in unmarked_secrets[:5]:  # Show first 5
            print(f"  - {os.path.relpath(file, ENVIRONMENT_DIR)}")
        if len(unmarked_secrets) > 5:
            print(f"  ... and {len(unmarked_secrets) - 5} more")

# Main execution
try:
    if COMMAND == 'encrypt':
        print("=== Encrypt Mode ===")
        if not DRY_RUN:
            create_backup()
        encrypt_in_place()
        show_status()
        
    elif COMMAND == 'decrypt':
        print("=== Decrypt Mode ===")

        if not DRY_RUN:
            create_backup()
        decrypt_in_place()
        show_status()
        
    elif COMMAND == 'diff-with-encrypted':
        # Show differences between current decrypted and encrypted files
        encrypted_files = find_secret_files(ENVIRONMENT_DIR, 'encrypted')
        
        with TemporaryDirectory() as tmpdir:
            for enc_file in encrypted_files:
                dec_file = enc_file.replace('.encrypted', '.decrypted')
                if os.path.exists(dec_file):
                    # Decrypt to temp and compare
                    tmp_dec = os.path.join(tmpdir, os.path.basename(enc_file) + '.tmp')
                    decrypt_file(enc_file, tmp_dec)
                    
                    result = subprocess.call(['diff', '--color', dec_file, tmp_dec])
                    if result != 0:
                        print(f"\nDifferences found in {os.path.basename(dec_file)}")
        
    elif COMMAND == 'diff-with-last-decrypted':
        # Compare with last backup
        all_backups = sorted(glob(f"{BACKUP_BASE_DIR}/*/"))
        if not all_backups:
            print("No backups found")
            exit(1)
        
        last_backup = all_backups[-1]
        print(f"Comparing with backup: {last_backup}")
        subprocess.call(['diff', '--color', '-r', 
                        f"{last_backup}{ENVIRONMENT}", 
                        ENVIRONMENT_DIR])
    
    elif COMMAND == 'cleanup':
        print("=== Cleanup Mode ===")
        cleanup_old_structure()
        
except Exception as e:
    print(f"\nERROR: Operation failed: {e}")
    traceback.print_exc()
    
    if not DRY_RUN and os.path.exists(BACKUP_DIR):
        print(f"\nRestoring from backup: {BACKUP_DIR}")
        if os.path.exists(ENVIRONMENT_DIR):
            shutil.rmtree(ENVIRONMENT_DIR)
        shutil.copytree(BACKUP_DIR, ENVIRONMENT_DIR)
        print("Restore completed")
    
    exit(1)