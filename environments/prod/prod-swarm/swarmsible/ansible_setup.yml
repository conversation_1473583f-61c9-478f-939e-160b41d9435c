---
- hosts: all
  vars:
    ansible_ssh_private_key_file: "{{ ansible_initial_ssh_private_key_file | default(playbook_dir + '/ssh_keys/root_rsa.decrypted') }}"
    ansible_user: "{{ ansible_initial_user | default('root') }}"
  gather_facts: False
  become: True
  tasks:
    - name: 'ansible required: install python'
      raw: python3 -c "import simplejson" || (DEBIAN_FRONTEND=noninteractive apt-get update -y && DEBIAN_FRONTEND=noninteractive apt-get install python3-minimal -y && DEBIAN_FRONTEND=noninteractive apt-get install python3-simplejson -y)

- hosts: all
  vars:
    ansible_ssh_private_key_file: "{{ ansible_initial_ssh_private_key_file | default(playbook_dir + '/ssh_keys/root_rsa.decrypted') }}"
    ansible_user: "{{ ansible_initial_user | default('root') }}"
  become: True
  tasks:
    - name: Ensure the en_US locale exists
      locale_gen:
        name: en_US.UTF-8
        state: present
    - name: set en_US as default locale
      command: update-locale set-locale LANG=en_US.UTF-8 LC_ALL=en_US.UTF-8

- hosts: all
  vars_files:
    - '{{ CWD }}/secrets/tailscale_secret.decrypted.yml'
  vars:
    ansible_ssh_private_key_file: "{{ ansible_initial_ssh_private_key_file | default(playbook_dir + '/ssh_keys/root_rsa.decrypted') }}"
    ansible_user: "{{ ansible_initial_user | default('root') }}"

    apt_update_cache: True
    apt_restart_after_dist_upgrade: True
  become: True
  roles:
    - role: full-apt-upgrade
    - role: network-setup
    - role: user-setup
      vars:
        configure_ssh_immediately: false
    - role: artis3n.tailscale.machine
      vars:
        tailscale_args: '--ssh'
        state: 'latest'
    - role: essential-software-setup

- hosts: all
  vars:
    ansible_ssh_private_key_file: "{{ global_ansible_ssh_private_key_file | default((project_base_dir | default(playbook_dir)) + '/ssh_keys/ansible_rsa.decrypted') }}"
    ansible_user: "{{ global_ansible_user | default('ansible') }}"
  become: True
  gather_facts: False
  tasks:
    - name: 'Get Tailscale IPv4 for this host'
      ansible.builtin.command: tailscale ip -4
      register: tailscale_ip_result
      changed_when: false
      retries: 10
      delay: 10
      until: tailscale_ip_result.stdout != ""

    - name: 'Verify Tailscale is fully operational'
      ansible.builtin.command: tailscale status --json
      register: tailscale_status_result
      changed_when: false
      retries: 5
      delay: 5
      until: tailscale_status_result.rc == 0

    - name: 'Reconfigure SSH to listen only on Tailscale interface'
      ansible.builtin.include_role:
        name: user-setup
        tasks_from: subtasks/sshd-config.yml

- hosts: all
  vars:
    ansible_ssh_private_key_file: "{{ global_ansible_ssh_private_key_file | default((project_base_dir | default(playbook_dir)) + '/ssh_keys/ansible_rsa.decrypted') }}"
    ansible_user: "{{ global_ansible_user | default('ansible') }}"

    apt_update_cache: True
    apt_restart_after_dist_upgrade: True
  become: True
  roles:
    - role: notnagel-user

- hosts: ansiblemanager
  vars:
    ansible_ssh_private_key_file: "{{ global_ansible_ssh_private_key_file | default((project_base_dir | default(playbook_dir)) + '/ssh_keys/ansible_rsa.decrypted') }}"
    ansible_user: "{{ global_ansible_user | default('ansible') }}"

    apt_update_cache: True
    apt_restart_after_dist_upgrade: True
  become: True
  roles:
    - docker-sysctl-tune
    - docker-setup
    - docker-pin-versions

- hosts: all
  vars:
    ansible_ssh_private_key_file: "{{ global_ansible_ssh_private_key_file | default((project_base_dir | default(playbook_dir)) + '/ssh_keys/ansible_rsa.decrypted') }}"
    ansible_user: "{{ global_ansible_user | default('ansible') }}"

    apt_update_cache: True
    apt_restart_after_dist_upgrade: True
  become: True
  roles:
    - role: developer-accounts

- hosts: all
  vars:
    ansible_ssh_private_key_file: "{{ global_ansible_ssh_private_key_file | default((project_base_dir | default(playbook_dir)) + '/ssh_keys/ansible_rsa.decrypted') }}"
    ansible_user: "{{ global_ansible_user | default('ansible') }}"
  become: True
  gather_facts: False
  tasks:
    - name: 'Final verification: Get Tailscale IPv4 for this host'
      ansible.builtin.command: tailscale ip -4
      register: tailscale_ip_result
      changed_when: false
      retries: 2
      delay: 5
      until: tailscale_ip_result.stdout != ""

    - name: 'Final verification: Check SSH connectivity via Tailscale'
      ansible.builtin.wait_for:
        host: "{{ tailscale_ip_result.stdout }}"
        port: 22
        timeout: 10
      delegate_to: localhost
      become: false
      
    - name: 'FINAL STEP: Configure firewall (placed at end for safety)'
      ansible.builtin.include_role:
        name: firewall
