---
# Tailscale Exit Node Configuration Playbook
# This playbook configures servers as Tailscale exit nodes
#
# Usage:
#   ansible-playbook -i inventory/docker_swarm_groups.yml -i inventory/ansible_tailscale_inventory.py \
#     ./swarmsible/tailscale_exit_node.yml --extra-vars="$EXTRA_VARS"
#
# Prerequisites:
# - Servers must be accessible via SSH
# - Tailscale must be installed and authenticated
# - UFW firewall should be configured
#
# Configuration:
# - Set tailscale_exit_node_enabled: true in host vars or group vars to enable exit node functionality

- hosts: all
  become: true
  vars:
    ansible_ssh_private_key_file: "{{ global_ansible_ssh_private_key_file | default((project_base_dir | default(playbook_dir)) + '/ssh_keys/ansible_rsa.decrypted') }}"
    ansible_user: "{{ global_ansible_user | default('ansible') }}"

  pre_tasks:
    - name: 'Check if Tailscale is available'
      ansible.builtin.command: which tailscale
      register: tailscale_check
      failed_when: false
      changed_when: false

    - name: 'Verify Tailscale is installed'
      ansible.builtin.fail:
        msg: "Tailscale is not installed on {{ inventory_hostname }}. Please install Tailscale first."
      when: tailscale_check.rc != 0

    - name: 'Get Tailscale status'
      ansible.builtin.command: tailscale status --json
      register: tailscale_status_check
      failed_when: false
      changed_when: false

    - name: 'Verify Tailscale is authenticated'
      ansible.builtin.fail:
        msg: "Tailscale is not authenticated on {{ inventory_hostname }}. Please authenticate Tailscale first."
      when: tailscale_status_check.rc != 0

    - name: 'Display current configuration'
      ansible.builtin.debug:
        msg: |
          Host: {{ inventory_hostname }}
          Tailscale Exit Node Enabled: {{ tailscale_exit_node_enabled | default(false) }}
          {% if tailscale_exit_node_enabled | default(false) | bool %}
          This host will be configured as a Tailscale exit node.
          {% else %}
          This host will NOT be configured as a Tailscale exit node.
          To enable, set tailscale_exit_node_enabled: true in host/group vars.
          {% endif %}

  roles:
    - role: tailscale-exit-node
    - role: firewall

  post_tasks:
    - name: 'Final verification for exit node hosts'
      when: tailscale_exit_node_enabled | default(false) | bool
      block:
        - name: 'Verify IP forwarding is enabled'
          ansible.builtin.command: sysctl net.ipv4.ip_forward net.ipv6.conf.all.forwarding
          register: ip_forward_check
          changed_when: false

        - name: 'Display IP forwarding status'
          ansible.builtin.debug:
            var: ip_forward_check.stdout_lines

        - name: 'Verify UFW masquerading rules'
          ansible.builtin.command: grep -A 5 "NAT table rules for Tailscale exit node" /etc/ufw/before.rules
          register: ufw_nat_check
          failed_when: false
          changed_when: false

        - name: 'Display UFW NAT rules status'
          ansible.builtin.debug:
            msg: "{{ 'UFW NAT masquerading rules are configured' if ufw_nat_check.rc == 0 else 'UFW NAT masquerading rules are NOT configured' }}"

        - name: 'Get final Tailscale status'
          ansible.builtin.command: tailscale status
          register: final_tailscale_status
          changed_when: false

        - name: 'Display final Tailscale status'
          ansible.builtin.debug:
            var: final_tailscale_status.stdout_lines
