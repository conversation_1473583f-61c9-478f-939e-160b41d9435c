---
- name: 'Reset UFW rules'
  community.general.ufw:
    state: reset
  notify: Reload UFW

- name: 'Set UFW default policies and enable UFW'
  community.general.ufw:
    state: enabled
    policy: '{{ item.policy }}'
    direction: '{{ item.direction }}'
  with_items:
    - { direction: 'incoming', policy: 'deny' }
    - { direction: 'outgoing', policy: 'allow' }
  notify: Reload UFW

- name: 'Allow all incoming access on tailscale0 interface'
  community.general.ufw:
    rule: allow
    interface: tailscale0
    direction: in
    comment: 'Allow all incoming from Tailscale private network'
  notify: Reload UFW

- name: 'Allow connections to SSH from management node ips'
  community.general.ufw:
    rule: allow
    from_ip: "{{ item }}"
    to_ip: 0.0.0.0/0
    to_port: "22"
    proto: tcp
    comment: "allow management node"
  with_items: "{{ management_node_ips | default([]) }}"
  when: ssh_only_management_nodes | default('False') | bool
  notify: Reload UFW

- name: 'Apply Cloudflare firewall rules'
  ansible.builtin.import_role:
    name: cloudflare-firewall

- name: 'Configure Tailscale exit node masquerading'
  when: tailscale_exit_node_enabled | default(false) | bool
  block:
    - name: 'Backup original UFW before.rules'
      ansible.builtin.copy:
        src: /etc/ufw/before.rules
        dest: /etc/ufw/before.rules.backup
        remote_src: true
        backup: false
      ignore_errors: true

    - name: 'Add NAT masquerading rules for Tailscale exit node'
      ansible.builtin.blockinfile:
        path: /etc/ufw/before.rules
        marker: '# {mark} ANSIBLE MANAGED BLOCK - Tailscale Exit Node NAT'
        insertafter: EOF
        block: |

          # NAT table rules for Tailscale exit node
          *nat
          :POSTROUTING ACCEPT [0:0]
          -A POSTROUTING -s **********/10 -j MASQUERADE
          COMMIT
      notify: Reload UFW
