---
- name: 'Reset UFW rules'
  community.general.ufw:
    state: reset
  notify: Reload UFW

- name: 'Set UFW default policies and enable UFW'
  community.general.ufw:
    state: enabled
    policy: '{{ item.policy }}'
    direction: '{{ item.direction }}'
  with_items:
    - { direction: 'incoming', policy: 'deny' }
    - { direction: 'outgoing', policy: 'allow' }
  notify: Reload UFW

- name: 'Allow all incoming access on tailscale0 interface'
  community.general.ufw:
    rule: allow
    interface: tailscale0
    direction: in
    comment: 'Allow all incoming from Tailscale private network'
  notify: Reload UFW

- name: 'Allow connections to SSH from management node ips'
  community.general.ufw:
    rule: allow
    from_ip: "{{ item }}"
    to_ip: 0.0.0.0/0
    to_port: "22"
    proto: tcp
    comment: "allow management node"
  with_items: "{{ management_node_ips | default([]) }}"
  when: ssh_only_management_nodes | default('False') | bool
  notify: Reload UFW

- name: 'Apply Cloudflare firewall rules'
  ansible.builtin.import_role:
    name: cloudflare-firewall
