---
- name: 'Configure IP forwarding for Tailscale exit node'
  when: tailscale_exit_node_enabled | default(false) | bool
  block:
    - name: 'Check if /etc/sysctl.d directory exists'
      ansible.builtin.stat:
        path: /etc/sysctl.d
      register: sysctl_d_dir

    - name: 'Configure IP forwarding using systemd sysctl approach'
      when: sysctl_d_dir.stat.exists
      block:
        - name: 'Create Tailscale sysctl configuration file'
          ansible.builtin.copy:
            content: |
              # IP forwarding configuration for Tailscale exit node
              net.ipv4.ip_forward = 1
              net.ipv6.conf.all.forwarding = 1
            dest: /etc/sysctl.d/99-tailscale.conf
            owner: root
            group: root
            mode: '0644'
          notify: 
            - Apply sysctl settings
            - Restart systemd-sysctl

        - name: 'Remove duplicate IP forwarding entries from /etc/sysctl.conf'
          ansible.builtin.lineinfile:
            path: /etc/sysctl.conf
            regexp: '^net\.ipv4\.ip_forward\s*=\s*1$'
            state: absent

        - name: 'Remove duplicate IPv6 forwarding entries from /etc/sysctl.conf'
          ansible.builtin.lineinfile:
            path: /etc/sysctl.conf
            regexp: '^net\.ipv6\.conf\.all\.forwarding\s*=\s*1$'
            state: absent

    - name: 'Configure IP forwarding using legacy sysctl.conf approach'
      when: not sysctl_d_dir.stat.exists
      block:
        - name: 'Enable IPv4 forwarding in /etc/sysctl.conf'
          ansible.posix.sysctl:
            name: net.ipv4.ip_forward
            value: '1'
            sysctl_file: /etc/sysctl.conf
            reload: true

        - name: 'Enable IPv6 forwarding in /etc/sysctl.conf'
          ansible.posix.sysctl:
            name: net.ipv6.conf.all.forwarding
            value: '1'
            sysctl_file: /etc/sysctl.conf
            reload: true

- name: 'Configure Tailscale as exit node'
  when: tailscale_exit_node_enabled | default(false) | bool
  block:
    - name: 'Check current Tailscale status'
      ansible.builtin.command: tailscale status --json
      register: tailscale_status_result
      changed_when: false
      failed_when: false

    - name: 'Parse Tailscale status'
      ansible.builtin.set_fact:
        tailscale_status: "{{ tailscale_status_result.stdout | from_json }}"
      when: tailscale_status_result.rc == 0

    - name: 'Check if exit node is already advertised'
      ansible.builtin.set_fact:
        exit_node_advertised: "{{ tailscale_status.Self.ExitNodeOption | default(false) }}"
      when: tailscale_status is defined

    - name: 'Advertise as Tailscale exit node'
      ansible.builtin.command: tailscale up --advertise-exit-node
      when: 
        - tailscale_status is defined
        - not (exit_node_advertised | default(false) | bool)
      register: tailscale_exit_node_result

    - name: 'Display exit node configuration result'
      ansible.builtin.debug:
        msg: "Tailscale exit node configuration: {{ 'already configured' if (exit_node_advertised | default(false) | bool) else 'newly configured' }}"
