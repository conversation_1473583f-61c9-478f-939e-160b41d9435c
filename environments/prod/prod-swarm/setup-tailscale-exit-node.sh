#!/bin/bash

# Tailscale Exit Node Setup Script
# 
# Usage:
#   ./setup-tailscale-exit-node.sh                    # Run on all servers
#   ./setup-tailscale-exit-node.sh hostname           # Run only on specific server
#   ./setup-tailscale-exit-node.sh "host1,host2"      # Run on multiple specific servers
#
# This script configures Tailscale exit node functionality:
# - Enables IP forwarding (IPv4 and IPv6)
# - Configures UFW masquerading rules for Tailscale subnet
# - Advertises the server as a Tailscale exit node
# - Verifies the configuration

check_result() {
    ___RESULT=$?
    if [ $___RESULT -ne 0 ]; then
        echo "$1"
        exit 1
    fi
}

echo "======================================"
echo "Tailscale Exit Node Configuration"
echo "======================================"

# Check if virtual environment exists and activate it
if [ -f "../../prod-venv/bin/activate" ]; then
    source ../../prod-venv/bin/activate
fi

set -a
export HCLOUD_TOKEN=$(yq -r .hcloud_token secrets/hcloud_token.decrypted.yml)
export NETCUP_CUSTOMER_ID=$(yq -r .customer_id secrets/netcup.decrypted.yml)
export NETCUP_WS_PASSWORD=$(yq -r .password secrets/netcup.decrypted.yml)

EXTRA_VARS="{ \"swarmsible_vars_path\": \"$(realpath vars)\", \"CWD\": \"$(pwd)\" }"

# Handle optional --limit parameter for specific hosts
LIMIT_HOSTS=""
if [ "$1" != "" ]; then
    LIMIT_HOSTS="--limit=$1"
    echo "Running on specific hosts: $1"
fi

echo ""
echo "This will configure Tailscale exit node functionality on the specified hosts:"
echo "- Enable IP forwarding (IPv4 and IPv6)"
echo "- Configure UFW masquerading rules for Tailscale subnet (**********/10)"
echo "- Advertise servers as Tailscale exit nodes"
echo "- Verify the configuration"
echo ""
echo "IMPORTANT: Make sure to enable exit node functionality by setting:"
echo "  tailscale_exit_node_enabled: true"
echo "in your host variables or group variables before running this script."
echo ""

read -p "Continue with Tailscale exit node configuration? (y/n) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Tailscale exit node configuration cancelled."
    exit 1
fi

echo ""
echo "Running Tailscale exit node configuration..."

# Run Tailscale exit node configuration using Tailscale inventory
ansible-playbook -i inventory/docker_swarm_groups.yml -i inventory/ansible_tailscale_inventory.py \
  ./swarmsible/tailscale_exit_node.yml --extra-vars="$EXTRA_VARS" $LIMIT_HOSTS

check_result "Failed to configure Tailscale exit node"

echo ""
echo "======================================"
echo "Tailscale Exit Node Configuration Complete!"
echo "======================================"
echo ""
echo "Configuration applied:"
echo "- IP forwarding enabled for IPv4 and IPv6"
echo "- UFW masquerading rules configured for Tailscale subnet"
echo "- Servers advertised as Tailscale exit nodes (where enabled)"
echo ""
echo "Next steps:"
echo "1. Go to Tailscale admin console (https://login.tailscale.com/admin/machines)"
echo "2. Find your configured servers and approve them as exit nodes"
echo "3. Test the exit node functionality from client devices"
echo ""
echo "To verify configuration on hosts, you can run:"
echo "ansible all -i inventory/docker_swarm_groups.yml -i inventory/ansible_tailscale_inventory.py -m shell -a 'tailscale status'"
echo "ansible all -i inventory/docker_swarm_groups.yml -i inventory/ansible_tailscale_inventory.py -m shell -a 'sysctl net.ipv4.ip_forward net.ipv6.conf.all.forwarding'"
