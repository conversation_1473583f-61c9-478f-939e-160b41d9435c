# Tailscale Exit Node Configuration

This document describes how to configure servers as Tailscale exit nodes using the automated Ansible configuration.

## Overview

A Tailscale exit node allows you to route all your internet traffic through a specific server in your Tailscale network. This is useful for:
- Accessing region-specific content
- Securing traffic when on untrusted networks
- Centralizing internet access through your infrastructure

## Prerequisites

1. **Tailscale installed and authenticated** on target servers
2. **UFW firewall configured** (using the existing firewall role)
3. **Ansible environment** set up and working

## Configuration Steps

### 1. Enable Exit Node for Specific Hosts

Create or edit host variable files to enable exit node functionality:

```bash
# Example: Configure netcup-manager-1 as an exit node
cat > inventory/host_vars/netcup-manager-1.yml << EOF
tailscale_exit_node_enabled: true
EOF
```

### 2. Run the Configuration Script

```bash
# Configure all hosts (only those with tailscale_exit_node_enabled: true will be affected)
./setup-tailscale-exit-node.sh

# Configure specific host only
./setup-tailscale-exit-node.sh netcup-manager-1

# Configure multiple specific hosts
./setup-tailscale-exit-node.sh "netcup-manager-1,hetzner-worker-1"
```

### 3. Approve Exit Nodes in Tailscale Admin Console

1. Go to [Tailscale Admin Console](https://login.tailscale.com/admin/machines)
2. Find your configured servers
3. Click on each server and enable "Use as exit node"

## What the Configuration Does

### IP Forwarding Configuration

The configuration uses the **systemd sysctl approach** (recommended):

```bash
# Creates /etc/sysctl.d/99-tailscale.conf with:
net.ipv4.ip_forward = 1
net.ipv6.conf.all.forwarding = 1

# Removes any duplicate entries from /etc/sysctl.conf
# Applies settings and restarts systemd-sysctl service
```

For systems without `/etc/sysctl.d/`, it falls back to the legacy `/etc/sysctl.conf` approach.

### UFW Masquerading Rules

Adds NAT masquerading rules to `/etc/ufw/before.rules`:

```bash
# NAT table rules for Tailscale exit node
*nat
:POSTROUTING ACCEPT [0:0]
-A POSTROUTING -s **********/10 -j MASQUERADE
COMMIT
```

### Tailscale Configuration

- Runs `tailscale up --advertise-exit-node` to advertise the server as an exit node
- Checks current status to avoid unnecessary reconfigurations

## Verification

### Check IP Forwarding

```bash
ansible all -i inventory/docker_swarm_groups.yml -i inventory/ansible_tailscale_inventory.py \
  -m shell -a 'sysctl net.ipv4.ip_forward net.ipv6.conf.all.forwarding'
```

### Check UFW Rules

```bash
ansible all -i inventory/docker_swarm_groups.yml -i inventory/ansible_tailscale_inventory.py \
  -m shell -a 'grep -A 5 "NAT table rules for Tailscale exit node" /etc/ufw/before.rules'
```

### Check Tailscale Status

```bash
ansible all -i inventory/docker_swarm_groups.yml -i inventory/ansible_tailscale_inventory.py \
  -m shell -a 'tailscale status'
```

## Troubleshooting

### Common Issues

1. **Internet connectivity lost after configuration**
   - Check UFW rules: `ufw status verbose`
   - Verify UFW is enabled: `ufw --force enable && ufw reload`
   - Check for conflicting routing rules

2. **Exit node not appearing in Tailscale admin**
   - Verify Tailscale is authenticated: `tailscale status`
   - Check if advertise-exit-node is active: `tailscale status --json | jq .Self.ExitNodeOption`
   - Re-run: `tailscale up --advertise-exit-node`

3. **IP forwarding not working**
   - Check sysctl settings: `sysctl net.ipv4.ip_forward net.ipv6.conf.all.forwarding`
   - Verify configuration files exist: `ls -la /etc/sysctl.d/99-tailscale.conf`
   - Manually apply: `sysctl -p /etc/sysctl.d/99-tailscale.conf`

### Manual Cleanup (if needed)

```bash
# Remove IP forwarding configuration
sudo rm -f /etc/sysctl.d/99-tailscale.conf
sudo systemctl restart systemd-sysctl

# Remove UFW masquerading rules
sudo cp /etc/ufw/before.rules.backup /etc/ufw/before.rules
sudo ufw reload

# Disable exit node advertising
tailscale up --advertise-exit-node=false
```

## Security Considerations

- Exit nodes can see all traffic routed through them
- Only configure trusted servers as exit nodes
- Monitor exit node usage and performance
- Consider implementing additional logging/monitoring for exit node traffic

## Integration with Existing Infrastructure

This configuration integrates seamlessly with the existing infrastructure:
- Uses the same Ansible inventory and SSH keys
- Extends the existing firewall role
- Follows the same configuration patterns as other services
- Can be combined with other playbooks and roles
