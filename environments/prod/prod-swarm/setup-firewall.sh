#!/bin/bash

# Firewall Setup Script
# 
# Usage:
#   ./setup-firewall.sh                    # Run on all servers
#   ./setup-firewall.sh hostname           # Run only on specific server
#   ./setup-firewall.sh "host1,host2"      # Run on multiple specific servers
#
# This script configures UFW firewall rules:
# - Blocks all incoming traffic except from Tailscale network
# - Allows Cloudflare IPs to access HTTP/HTTPS ports
# - Allows all outgoing traffic
# - Configures Tailscale exit node masquerading (if enabled)

check_result() {
    ___RESULT=$?
    if [ $___RESULT -ne 0 ]; then
        echo "$1"
        exit 1
    fi
}

echo "======================================"
echo "Firewall Configuration Setup"
echo "======================================"

# Check if virtual environment exists and activate it
if [ -f "../../prod-venv/bin/activate" ]; then
    source ../../prod-venv/bin/activate
fi

set -a
export HCLOUD_TOKEN=$(yq -r .hcloud_token secrets/hcloud_token.decrypted.yml)
export NETCUP_CUSTOMER_ID=$(yq -r .customer_id secrets/netcup.decrypted.yml)
export NETCUP_WS_PASSWORD=$(yq -r .password secrets/netcup.decrypted.yml)

EXTRA_VARS="{ \"swarmsible_vars_path\": \"$(realpath vars)\", \"CWD\": \"$(pwd)\" }"

# Handle optional --limit parameter for specific hosts
LIMIT_HOSTS=""
if [ "$1" != "" ]; then
    LIMIT_HOSTS="--limit=$1"
    echo "Running on specific hosts: $1"
fi

echo ""
echo "This will configure firewall rules on the specified hosts:"
echo "- Reset all existing UFW rules"
echo "- Set default policy: deny incoming, allow outgoing"
echo "- Allow all traffic from Tailscale network"
echo "- Allow Cloudflare IPs to access HTTP/HTTPS ports"
echo "- Configure Tailscale exit node masquerading (if tailscale_exit_node_enabled: true)"
echo ""

read -p "Continue with firewall configuration? (y/n) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Firewall configuration cancelled."
    exit 1
fi

echo ""
echo "Running firewall configuration..."

# Run firewall configuration using Tailscale inventory like setup-phase2.sh
ansible-playbook -i inventory/docker_swarm_groups.yml -i inventory/ansible_tailscale_inventory.py \
  ./swarmsible/firewall.yml --extra-vars="$EXTRA_VARS" $LIMIT_HOSTS

check_result "Failed to configure firewall"

echo ""
echo "======================================"
echo "Firewall Configuration Complete!"
echo "======================================"
echo ""
echo "UFW firewall has been configured with:"
echo "- Default deny for incoming traffic"
echo "- Allow all traffic from Tailscale network"
echo "- Allow Cloudflare IPs for HTTP/HTTPS"
echo "- Tailscale exit node masquerading (where enabled)"
echo ""
echo "To verify firewall status on hosts, you can run:"
echo "ansible all -i inventory/docker_swarm_groups.yml -i inventory/ansible_tailscale_inventory.py -m shell -a 'ufw status verbose'"
echo ""
echo "For Tailscale exit node configuration, use:"
echo "./setup-tailscale-exit-node.sh"